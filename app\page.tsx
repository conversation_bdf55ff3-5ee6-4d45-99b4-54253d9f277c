"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ArrowR<PERSON>, TrendingUp, Moon, Sun, DollarSign, Zap, Users, Award, Globe, User, LogIn, Flame, Gift, Newspaper } from "lucide-react"
import { useState, useEffect, useRef } from "react"
import Link from "next/link"
import { useCounter } from "@/hooks/use-counter"

import CompareSection from "@/components/compare-section"
import AwardsSection from "@/components/awards-section"
import FAQSection from "@/components/faq-section"
import PricingSection from "@/components/pricing-section"
import CookieConsent from "@/components/cookie-consent"
import LanguageSupport from "@/components/language-support"
import Footer from "@/components/footer"
import RealTimeForex from "@/components/real-time-forex"
import LanguageSelector from "@/components/language-selector"
import PayoutShowcase from "@/components/payout-showcase"
import { useLanguage } from "@/contexts/language-context"
import Image from "next/image"

// Animated Counter Component
const AnimatedCounter = ({ end, start = 0, duration = 2000, delay = 0, suffix = '', prefix = '' }: {
  end: number
  start?: number
  duration?: number
  delay?: number
  suffix?: string
  prefix?: string
}) => {
  const { formattedCount } = useCounter({ end, start, duration, delay, suffix, prefix })
  return <span>{formattedCount}</span>
}

export default function ForexPropFirmLanding() {
  const [isDarkMode, setIsDarkMode] = useState(true)
  const [scrollY, setScrollY] = useState(0)
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const [isHoveringTradingElement, setIsHoveringTradingElement] = useState(false)
  const scrollRef = useRef<NodeJS.Timeout | null>(null)
  const mouseRef = useRef<NodeJS.Timeout | null>(null)
  const { t, isRTL } = useLanguage()

  // Check system preference on initial load
  useEffect(() => {
    if (typeof window !== "undefined") {
      const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches
      setIsDarkMode(prefersDark)
    }
  }, [])

  // Handle scroll for morphing animation with debounce
  useEffect(() => {
    const handleScroll = () => {
      if (scrollRef.current) {
        clearTimeout(scrollRef.current)
      }

      scrollRef.current = setTimeout(() => {
        setScrollY(window.scrollY)
      }, 10)
    }

    window.addEventListener("scroll", handleScroll, { passive: true })
    return () => {
      if (scrollRef.current) clearTimeout(scrollRef.current)
      window.removeEventListener("scroll", handleScroll)
    }
  }, [])

  // Track mouse position for magnetic effects with debounce
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (mouseRef.current) {
        clearTimeout(mouseRef.current)
      }

      mouseRef.current = setTimeout(() => {
        setMousePosition({ x: e.clientX, y: e.clientY })
      }, 10)
    }

    window.addEventListener("mousemove", handleMouseMove, { passive: true })
    return () => {
      if (mouseRef.current) clearTimeout(mouseRef.current)
      window.removeEventListener("mousemove", handleMouseMove)
    }
  }, [])

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode)
    document.documentElement.classList.toggle("dark")
  }

  // Calculate morphing progress based on scroll
  const getShapeProgress = () => {
    if (typeof window === "undefined") return { borderRadius: "50%", rotation: "0deg" }

    const windowHeight = window.innerHeight
    const totalScrollHeight = document.documentElement.scrollHeight - windowHeight

    if (totalScrollHeight <= 0) return { borderRadius: "50%", rotation: "0deg" }

    const firstTransition = Math.min(scrollY / (totalScrollHeight * 0.4), 1)
    const secondTransitionStart = totalScrollHeight * 0.6
    const secondTransition = Math.max(0, Math.min((scrollY - secondTransitionStart) / (totalScrollHeight * 0.4), 1))

    let borderRadius = "50%"
    if (secondTransition > 0) {
      borderRadius = `${secondTransition * 50}%`
    } else {
      borderRadius = `${(1 - firstTransition) * 50}%`
    }

    const rotation = `${firstTransition * 20 - secondTransition * 20}deg`

    return { borderRadius, rotation }
  }

  const { borderRadius, rotation } = getShapeProgress()

  return (
    <div
      className={`min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-slate-950 dark:via-gray-900 dark:to-slate-950 text-gray-900 dark:text-white overflow-hidden relative transition-colors duration-500 ${
        isHoveringTradingElement ? "cursor-crosshair" : "cursor-default"
      } ${isRTL ? "rtl" : "ltr"}`}
    >
      {/* Custom CSS for enhanced UX */}
      <style jsx global>{`
        ::selection {
          background: ${isDarkMode ? "rgba(59, 130, 246, 0.3)" : "rgba(37, 99, 235, 0.2)"};
          color: ${isDarkMode ? "#ffffff" : "#1f2937"};
        }
        
        /* Custom scrollbar */
        ::-webkit-scrollbar {
          width: 6px;
        }
        ::-webkit-scrollbar-track {
          background: ${isDarkMode ? "rgba(15, 23, 42, 0.1)" : "rgba(243, 244, 246, 0.5)"};
        }
        ::-webkit-scrollbar-thumb {
          background: ${isDarkMode ? "rgba(59, 130, 246, 0.3)" : "rgba(37, 99, 235, 0.3)"};
          border-radius: 3px;
        }
        ::-webkit-scrollbar-thumb:hover {
          background: ${isDarkMode ? "rgba(59, 130, 246, 0.5)" : "rgba(37, 99, 235, 0.5)"};
        }

        /* Breathing animation */
        @keyframes subtle-breathe {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.01); }
        }
        
        .subtle-breathe {
          animation: subtle-breathe 6s ease-in-out infinite;
          will-change: transform;
        }

        /* Hardware acceleration for performance */
        .hw-accelerate {
          transform: translateZ(0);
          will-change: transform;
        }
      `}</style>

      {/* Artistic Background */}
      <div className="fixed inset-0 bg-[radial-gradient(ellipse_at_center,rgba(59,130,246,0.05),rgba(255,255,255,0))] dark:bg-[radial-gradient(ellipse_at_center,rgba(59,130,246,0.15),rgba(0,0,0,0))]" />
      <div className="fixed top-0 left-0 w-full h-full">
        <div className="absolute top-[10%] left-[5%] w-32 md:w-64 h-32 md:h-64 rounded-full bg-gradient-to-r from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10 blur-3xl subtle-breathe" />
        <div
          className="absolute top-[40%] right-[10%] w-40 md:w-80 h-40 md:h-80 rounded-full bg-gradient-to-r from-indigo-500/5 to-blue-500/5 dark:from-indigo-500/10 dark:to-blue-500/10 blur-3xl subtle-breathe"
          style={{ animationDelay: "1s" }}
        />
        <div
          className="absolute bottom-[15%] left-[15%] w-36 md:w-72 h-36 md:h-72 rounded-full bg-gradient-to-r from-cyan-500/5 to-teal-500/5 dark:from-cyan-500/10 dark:to-teal-500/10 blur-3xl subtle-breathe"
          style={{ animationDelay: "2s" }}
        />
      </div>

      {/* Main Content */}
      <main className="relative z-10">
        {/* Responsive Navigation */}
        <nav
          className="fixed top-4 left-4 right-4 z-40 flex items-center justify-between"
          role="navigation"
          aria-label="Main navigation"
        >
          {/* Logo */}
          <Link href="/" className="flex items-center gap-3 group">
            <div className="w-10 h-10 md:w-12 md:h-12 rounded-2xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
              <TrendingUp className="w-5 h-5 md:w-6 md:h-6 text-white" />
            </div>
            <div className="hidden sm:block">
              <h1 className="text-xl md:text-2xl font-bold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                Forex Throne
              </h1>
              <p className="text-xs text-gray-600 dark:text-white/60">Prop Trading Firm</p>
            </div>
          </Link>

          {/* Navigation Links */}
          <div className="flex items-center gap-3 md:gap-6">
            <LanguageSelector />
            <Button
              variant="ghost"
              onClick={toggleTheme}
              className="text-sm md:text-lg font-light text-gray-600 dark:text-white/70 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/50 dark:hover:bg-white/5 transition-all duration-300 px-2 md:px-4 rounded-full group"
              aria-label="Toggle between light and dark theme"
            >
              <div className="group-hover:rotate-180 transition-transform duration-500">
                {isDarkMode ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
              </div>
            </Button>
            <Link href="/trading-symbols">
              <Button
                variant="ghost"
                className="text-sm md:text-lg font-light text-gray-600 dark:text-white/70 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/50 dark:hover:bg-white/5 transition-all duration-300 px-2 md:px-4"
              >
                {t("nav.markets")}
              </Button>
            </Link>
            <Link href="/economic-calendar">
              <Button
                variant="ghost"
                className="text-sm md:text-lg font-light text-gray-600 dark:text-white/70 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/50 dark:hover:bg-white/5 transition-all duration-300 px-2 md:px-4"
              >
                {t("nav.calendar")}
              </Button>
            </Link>
            <Link href="/affiliate">
              <Button
                variant="ghost"
                className="text-sm md:text-lg font-light text-gray-600 dark:text-white/70 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/50 dark:hover:bg-white/5 transition-all duration-300 px-2 md:px-4"
              >
                {t("nav.affiliate")}
              </Button>
            </Link>
            <Link href="/giveaway">
              <Button
                variant="ghost"
                className="text-sm md:text-lg font-light text-gray-600 dark:text-white/70 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/50 dark:hover:bg-white/5 transition-all duration-300 px-2 md:px-4"
              >
                <Gift className="h-4 w-4 mr-2" />
                Giveaway
              </Button>
            </Link>
            <Link href="/news">
              <Button
                variant="ghost"
                className="text-sm md:text-lg font-light text-gray-600 dark:text-white/70 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/50 dark:hover:bg-white/5 transition-all duration-300 px-2 md:px-4"
              >
                <Newspaper className="h-4 w-4 mr-2" />
                News
              </Button>
            </Link>
            <Link href="/auth">
              <Button
                variant="ghost"
                className="text-sm md:text-lg font-light text-gray-600 dark:text-white/70 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/50 dark:hover:bg-white/5 transition-all duration-300 px-2 md:px-4"
              >
                <LogIn className="h-4 w-4 mr-2" />
                {t("nav.login")}
              </Button>
            </Link>
            <Link href="/auth?mode=signup">
              <Button className="rounded-full bg-gray-900 dark:bg-white text-white dark:text-black hover:bg-gray-800 dark:hover:bg-white/90 px-3 md:px-6 py-1.5 md:py-2 text-sm md:text-base hover:scale-105 transition-all duration-300 hover:shadow-lg">
                <User className="h-4 w-4 mr-2" />
                {t("nav.getFunded")}
              </Button>
            </Link>
          </div>
        </nav>

        {/* Creative Hero Section */}
        <section className="min-h-screen flex items-center justify-center px-8 md:px-12 lg:px-16 relative pt-20 md:pt-0">
          {/* Background lines like Ready to Trade section */}
          <div className="absolute inset-0 flex items-center justify-center -z-10">
            <div className="w-[300px] md:w-[500px] lg:w-[600px] h-[300px] md:h-[500px] lg:h-[600px] rounded-full border border-gray-200 dark:border-white/10 subtle-breathe" />
            <div
              className="w-[400px] md:w-[650px] lg:w-[800px] h-[400px] md:h-[650px] lg:h-[800px] rounded-full border border-gray-100 dark:border-white/5 absolute subtle-breathe"
              style={{ animationDelay: "1s" }}
            />
            <div
              className="w-[500px] md:w-[800px] lg:w-[1000px] h-[500px] md:h-[800px] lg:h-[1000px] rounded-full border border-gray-300 dark:border-white/3 absolute subtle-breathe"
              style={{ animationDelay: "2s" }}
            />
          </div>

          {/* Morphing Circles/Squares */}
          <div
            className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[400px] md:w-[600px] lg:w-[800px] h-[400px] md:h-[600px] lg:h-[800px] border border-gray-200 dark:border-white/5 transition-all duration-500 ease-out hw-accelerate"
            style={{
              borderRadius,
              transform: `translate(-50%, -50%) rotate(${rotation})`,
            }}
          />
          <div
            className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[300px] md:w-[450px] lg:w-[600px] h-[300px] md:h-[450px] lg:h-[600px] border border-gray-200 dark:border-white/10 transition-all duration-500 ease-out hw-accelerate"
            style={{
              borderRadius,
              transform: `translate(-50%, -50%) rotate(${rotation === "0deg" ? "0deg" : `-${rotation}`})`,
            }}
          />
          <div
            className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[200px] md:w-[300px] lg:w-[400px] h-[200px] md:h-[300px] lg:h-[400px] border border-gray-300 dark:border-white/20 transition-all duration-500 ease-out hw-accelerate"
            style={{
              borderRadius,
              transform: `translate(-50%, -50%) rotate(${rotation === "0deg" ? "0deg" : `${Number.parseFloat(rotation) * 0.5}deg`})`,
            }}
          />

          <div className="max-w-6xl mx-auto text-center relative">
            <h1 className="text-[6rem] md:text-[10rem] lg:text-[12rem] font-bold leading-none tracking-tighter mb-8 md:mb-12 group cursor-default">
              <span className="block text-gray-900 dark:text-white group-hover:tracking-wide transition-all duration-500">
                {t("hero.title1")}
              </span>
              <span className="block bg-gradient-to-r from-blue-600 via-cyan-600 to-indigo-600 dark:from-blue-400 dark:via-cyan-400 dark:to-indigo-400 bg-clip-text text-transparent group-hover:tracking-wide transition-all duration-500">
                {t("hero.title2")}
              </span>
            </h1>

            <p className="text-lg md:text-2xl lg:text-3xl text-gray-700 dark:text-white/80 mb-12 md:mb-16 max-w-3xl mx-auto leading-relaxed font-light">
              {t("hero.subtitle")}
            </p>

            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 via-cyan-600 to-indigo-600 dark:from-blue-500 dark:via-cyan-500 dark:to-indigo-500 p-[1px] rounded-full group hover:scale-105 transition-all duration-300 hover:shadow-xl">
              <Link href="/auth?mode=signup">
                <Button className="rounded-full bg-white dark:bg-black text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-black/90 px-6 md:px-8 py-4 md:py-6 text-lg md:text-xl group">
                  {t("hero.cta")}
                  <ArrowRight className="ml-2 h-4 w-4 md:h-5 md:w-5 group-hover:translate-x-1 transition-transform duration-300" />
                </Button>
              </Link>
            </div>

            {/* Stats Section - Integrated into Hero */}
            <div className="mt-16 md:mt-20">
              <div className="grid grid-cols-3 gap-4 max-w-2xl mx-auto">
                <div className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-1">
                    <AnimatedCounter end={136400} suffix="+" />
                  </div>
                  <div className="text-gray-600 dark:text-white/60 text-xs font-medium">FundedNext Accounts</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-1">
                    <AnimatedCounter end={47300} suffix="+" />
                  </div>
                  <div className="text-gray-600 dark:text-white/60 text-xs font-medium">Rewarded Traders</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-1">
                    <AnimatedCounter end={*********} prefix="$" suffix="+" />
                  </div>
                  <div className="text-gray-600 dark:text-white/60 text-xs font-medium">Total Rewarded</div>
                </div>
              </div>
            </div>

            {/* New Product Announcement - Integrated into Hero */}
            <div className="mt-8 md:mt-12 max-w-4xl mx-auto">
              <div className="bg-gradient-to-r from-blue-600 via-cyan-600 to-indigo-600 dark:from-blue-500 dark:via-cyan-500 dark:to-indigo-500 rounded-2xl p-6 md:p-8 relative overflow-hidden">
                <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1)_1px,transparent_1px)] bg-[length:20px_20px]" />
                
                <Badge className="mb-4 bg-white/20 text-white border-white/30 px-3 py-1 inline-flex items-center text-xs">
                  <Flame className="w-3 h-3 mr-1" />
                  NEWLY LAUNCHED
                </Badge>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 items-center">
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <div className="text-white font-bold text-lg">FOREX THRONE</div>
                      <div className="bg-white/20 text-white px-2 py-1 rounded-full text-xs font-medium">Futures</div>
                    </div>
                    
                    {/* Supported Platforms - Inside Product Announcement */}
                    <div className="mt-4">
                      <p className="text-white/80 text-xs font-medium mb-3">Supported Trading Platforms</p>
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2 bg-white/10 rounded-xl px-3 py-2">
                          <Image
                            src="https://res.cloudinary.com/dufcjjaav/image/upload/v1752130069/512x512bb-removebg-preview_qnfrll.png"
                            alt="MetaTrader 4"
                            width={28}
                            height={28}
                            className="w-7 h-7 object-contain"
                          />
                          <span className="text-white text-xs font-medium">MT4</span>
                        </div>
                        <div className="flex items-center gap-2 bg-white/10 rounded-xl px-3 py-2">
                          <Image
                            src="https://res.cloudinary.com/dufcjjaav/image/upload/v1752130069/MT5-removebg-preview_1_lqagz7.png"
                            alt="MetaTrader 5"
                            width={28}
                            height={28}
                            className="w-7 h-7 object-contain"
                          />
                          <span className="text-white text-xs font-medium">MT5</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <p className="text-white/90 text-sm leading-relaxed">
                      Master the markets, master your future. Introducing Forex Throne Futures, a new opportunity for our traders to become a part of our trading revolution by trading Futures!
                    </p>
                    <Button className="bg-white text-blue-600 hover:bg-white/90 px-4 py-2 rounded-full font-semibold text-sm">
                      Discover futures →
                      <ArrowRight className="ml-2 h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <RealTimeForex />

        {/* How Evaluation Challenges Work Section */}
        <section className="py-8 md:py-12 relative">
          <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16">
            {/* Section Header */}
            <div className="text-center mb-8 md:mb-12">
              <h2 className="text-2xl md:text-4xl lg:text-5xl font-bold mb-4 md:mb-6 leading-tight text-gray-900 dark:text-white">
                How our challenges work
              </h2>
            </div>

            {/* Three Steps */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 mb-8">
              {/* Step 1: Buy a Challenge */}
              <div className="text-center group">
                <div className="w-12 h-12 md:w-16 md:h-16 rounded-xl bg-gradient-to-r from-blue-600 via-cyan-600 to-indigo-600 dark:from-blue-500 dark:via-cyan-500 dark:to-indigo-500 flex items-center justify-center mx-auto mb-3 shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-6 h-6 md:w-8 md:h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2z"/>
                  </svg>
                  </div>
                <h3 className="text-base md:text-lg font-bold text-gray-900 dark:text-white mb-2">
                  Buy a challenge
                  </h3>
                <p className="text-gray-700 dark:text-white/70 text-xs leading-relaxed">
                  We offer a range of options designed to suit different trading approaches, experience levels and goals.
                </p>
                  </div>

              {/* Step 2: Demonstrate Skills */}
              <div className="text-center group">
                <div className="w-12 h-12 md:w-16 md:h-16 rounded-xl bg-gradient-to-r from-blue-600 via-cyan-600 to-indigo-600 dark:from-blue-500 dark:via-cyan-500 dark:to-indigo-500 flex items-center justify-center mx-auto mb-3 shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-6 h-6 md:w-8 md:h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                  </div>
                <h3 className="text-base md:text-lg font-bold text-gray-900 dark:text-white mb-2">
                  Demonstrate your skills
                  </h3>
                <p className="text-gray-700 dark:text-white/70 text-xs leading-relaxed">
                  Apply your knowledge and showcase your trading experience. Prove yourself and navigate market conditions, manage risk, and execute your strategy within the challenge rules.
                </p>
                  </div>

              {/* Step 3: Start Trading */}
              <div className="text-center group">
                <div className="w-12 h-12 md:w-16 md:h-16 rounded-xl bg-gradient-to-r from-blue-600 via-cyan-600 to-indigo-600 dark:from-blue-500 dark:via-cyan-500 dark:to-indigo-500 flex items-center justify-center mx-auto mb-3 shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-6 h-6 md:w-8 md:h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                  </div>
                <h3 className="text-base md:text-lg font-bold text-gray-900 dark:text-white mb-2">
                  Start Trading
                  </h3>
                <p className="text-gray-700 dark:text-white/70 text-xs leading-relaxed">
                  Successfully complete the challenge to gain access to a DNA Funded trading account.
                </p>
                      </div>
                    </div>

            {/* CTA Button */}
            <div className="text-center">
              <Button className="bg-gradient-to-r from-blue-600 via-cyan-600 to-indigo-600 dark:from-blue-500 dark:via-cyan-500 dark:to-indigo-500 hover:from-blue-700 hover:via-cyan-700 hover:to-indigo-700 text-white px-4 md:px-6 py-2 md:py-3 text-sm md:text-base font-semibold rounded-full hover:scale-105 transition-all duration-300 hover:shadow-xl">
                Get Funded
              </Button>
                      </div>
                    </div>
        </section>

        {/* Comparison Section */}
        <section className="py-8 md:py-12 relative">
          <div className="max-w-7xl mx-auto px-8 md:px-12 lg:px-16">
            {/* Section Header */}
            <div className="text-center mb-8 md:mb-12">
              <h2 className="text-2xl md:text-4xl lg:text-5xl font-bold mb-4 md:mb-6 leading-tight text-gray-900 dark:text-white">
                How we compare?
              </h2>
              <p className="text-sm md:text-base lg:text-lg text-gray-700 dark:text-white/70 leading-relaxed max-w-4xl mx-auto">
                Experience why Forex Throne will be a leader in the industry compared to the current competitors
              </p>
                  </div>

            {/* Comparison Table */}
            <div className="bg-white dark:bg-gray-900 rounded-xl shadow-xl overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200 dark:border-gray-700">
                      <th className="text-left p-3 md:p-4 font-bold text-gray-900 dark:text-white text-sm">
                        Features
                      </th>
                      <th className="text-center p-3 md:p-4 font-bold text-white text-sm bg-gradient-to-r from-blue-600 via-cyan-600 to-indigo-600 dark:from-blue-500 dark:via-cyan-500 dark:to-indigo-500">
                        Forex Throne
                      </th>
                      <th className="text-center p-3 md:p-4 font-bold text-gray-900 dark:text-white text-sm">
                        FTMO
                      </th>
                      <th className="text-center p-3 md:p-4 font-bold text-gray-900 dark:text-white text-sm">
                        FundedNext
                      </th>
                      <th className="text-center p-3 md:p-4 font-bold text-gray-900 dark:text-white text-sm">
                        FUNDING PIPS
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-b border-gray-100 dark:border-gray-800">
                      <td className="p-3 md:p-4 font-semibold text-gray-900 dark:text-white text-sm">Broker Backed</td>
                      <td className="text-center p-3 md:p-4 bg-blue-50 dark:bg-blue-900/20">
                        <span className="text-green-600 dark:text-green-400 font-semibold text-sm">Yes</span>
                      </td>
                      <td className="text-center p-3 md:p-4">
                        <span className="text-red-600 dark:text-red-400 font-semibold text-sm">No</span>
                      </td>
                      <td className="text-center p-3 md:p-4">
                        <span className="text-red-600 dark:text-red-400 font-semibold text-sm">No</span>
                      </td>
                      <td className="text-center p-3 md:p-4">
                        <span className="text-red-600 dark:text-red-400 font-semibold text-sm">No</span>
                      </td>
                    </tr>
                    <tr className="border-b border-gray-100 dark:border-gray-800">
                      <td className="p-3 md:p-4 font-semibold text-gray-900 dark:text-white text-sm">Rapid Evaluation</td>
                      <td className="text-center p-3 md:p-4 bg-blue-50 dark:bg-blue-900/20">
                        <span className="text-green-600 dark:text-green-400 font-semibold text-sm">Yes</span>
                      </td>
                      <td className="text-center p-3 md:p-4">
                        <span className="text-red-600 dark:text-red-400 font-semibold text-sm">No</span>
                      </td>
                      <td className="text-center p-3 md:p-4">
                        <span className="text-red-600 dark:text-red-400 font-semibold text-sm">No</span>
                      </td>
                      <td className="text-center p-3 md:p-4">
                        <span className="text-red-600 dark:text-red-400 font-semibold text-sm">No</span>
                      </td>
                    </tr>
                    <tr className="border-b border-gray-100 dark:border-gray-800">
                      <td className="p-3 md:p-4 font-semibold text-gray-900 dark:text-white text-sm">Max Allocation</td>
                      <td className="text-center p-3 md:p-4 bg-blue-50 dark:bg-blue-900/20">
                        <span className="text-blue-600 dark:text-blue-400 font-bold text-base">$600k</span>
                      </td>
                      <td className="text-center p-3 md:p-4">
                        <span className="text-gray-900 dark:text-white font-semibold text-sm">$400k</span>
                      </td>
                      <td className="text-center p-3 md:p-4">
                        <span className="text-gray-900 dark:text-white font-semibold text-sm">$300k</span>
                      </td>
                      <td className="text-center p-3 md:p-4">
                        <span className="text-gray-900 dark:text-white font-semibold text-sm">$300k</span>
                      </td>
                    </tr>
                    <tr className="border-b border-gray-100 dark:border-gray-800">
                      <td className="p-3 md:p-4 font-semibold text-gray-900 dark:text-white text-sm">Minimum Trading Days</td>
                      <td className="text-center p-3 md:p-4 bg-blue-50 dark:bg-blue-900/20">
                        <span className="text-blue-600 dark:text-blue-400 font-bold text-sm">3*</span>
                      </td>
                      <td className="text-center p-3 md:p-4">
                        <span className="text-gray-900 dark:text-white font-semibold text-sm">5</span>
                      </td>
                      <td className="text-center p-3 md:p-4">
                        <span className="text-gray-900 dark:text-white font-semibold text-sm">5</span>
                      </td>
                      <td className="text-center p-3 md:p-4">
                        <span className="text-gray-900 dark:text-white font-semibold text-sm">3</span>
                      </td>
                    </tr>
                    <tr className="border-b border-gray-100 dark:border-gray-800">
                      <td className="p-3 md:p-4 font-semibold text-gray-900 dark:text-white text-sm">Weekend Holding</td>
                      <td className="text-center p-3 md:p-4 bg-blue-50 dark:bg-blue-900/20">
                        <span className="text-green-600 dark:text-green-400 font-bold text-sm">Yes**</span>
                      </td>
                      <td className="text-center p-3 md:p-4">
                        <span className="text-red-600 dark:text-red-400 font-semibold text-sm">No</span>
                      </td>
                      <td className="text-center p-3 md:p-4">
                        <span className="text-green-600 dark:text-green-400 font-semibold text-sm">Yes</span>
                      </td>
                      <td className="text-center p-3 md:p-4">
                        <span className="text-green-600 dark:text-green-400 font-semibold text-sm">Yes</span>
                      </td>
                    </tr>
                    <tr>
                      <td className="p-3 md:p-4 font-semibold text-gray-900 dark:text-white text-sm">1 Step Challenge</td>
                      <td className="text-center p-3 md:p-4 bg-blue-50 dark:bg-blue-900/20">
                        <span className="text-green-600 dark:text-green-400 font-semibold text-sm">Yes</span>
                      </td>
                      <td className="text-center p-3 md:p-4">
                        <span className="text-red-600 dark:text-red-400 font-semibold text-sm">No</span>
                      </td>
                      <td className="text-center p-3 md:p-4">
                        <span className="text-green-600 dark:text-green-400 font-semibold text-sm">Yes</span>
                      </td>
                      <td className="text-center p-3 md:p-4">
                        <span className="text-red-600 dark:text-red-400 font-semibold text-sm">No</span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            {/* Footer Notes */}
            <div className="text-center mt-4">
              <p className="text-xs text-gray-600 dark:text-white/60">
                * Minimum trading days may vary based on account size
              </p>
              <p className="text-xs text-gray-600 dark:text-white/60 mt-1">
                ** Weekend holding available on select account types
              </p>
            </div>
          </div>
        </section>

        {/* Existing Sections */}
        <PayoutShowcase />
        <PricingSection />
        <LanguageSupport />
        <FAQSection />

        {/* Call to Action */}
        <section
          className="min-h-screen flex items-center justify-center relative py-24 md:py-32"
          aria-labelledby="cta-heading"
        >
          <div className="absolute inset-0 flex items-center justify-center -z-10">
            <div className="w-[300px] md:w-[500px] lg:w-[600px] h-[300px] md:h-[500px] lg:h-[600px] rounded-full border border-gray-200 dark:border-white/10 subtle-breathe" />
            <div
              className="w-[400px] md:w-[650px] lg:w-[800px] h-[400px] md:h-[650px] lg:h-[800px] rounded-full border border-gray-100 dark:border-white/5 absolute subtle-breathe"
              style={{ animationDelay: "1s" }}
            />
            <div
              className="w-[500px] md:w-[800px] lg:w-[1000px] h-[500px] md:h-[800px] lg:h-[1000px] rounded-full border border-gray-300 dark:border-white/3 absolute subtle-breathe"
              style={{ animationDelay: "2s" }}
            />
          </div>

          <div className="max-w-4xl mx-auto text-center px-8 md:px-12 lg:px-16 relative z-10">
            <h2
              id="cta-heading"
              className="text-5xl md:text-7xl lg:text-8xl font-bold mb-12 md:mb-16 leading-tight text-gray-900 dark:text-white"
            >
              Ready to{" "}
              <span className="bg-gradient-to-r from-blue-600 via-cyan-600 to-indigo-600 dark:from-blue-400 dark:via-cyan-400 dark:to-indigo-400 bg-clip-text text-transparent">
                Trade
              </span>
              ?
            </h2>
            <p className="text-xl md:text-2xl lg:text-3xl text-gray-700 dark:text-white/70 mb-16 md:mb-20 leading-relaxed">
              Join thousands of funded traders. Pass our evaluation and start trading with up to $400K in capital.
            </p>

            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 via-cyan-600 to-indigo-600 dark:from-blue-500 dark:via-cyan-500 dark:to-indigo-500 p-[1px] rounded-full group hover:scale-105 transition-all duration-300 hover:shadow-xl">
              <Link href="/auth?mode=signup">
                <Button className="rounded-full bg-white dark:bg-black text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-black/90 px-8 md:px-12 py-6 md:py-8 text-lg md:text-2xl group">
                  Get Funded Today
                  <ArrowRight className="ml-2 md:ml-3 h-5 w-5 md:h-6 md:w-6 group-hover:translate-x-1 transition-transform duration-300" />
                </Button>
              </Link>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <Footer />

      {/* Cookie Consent */}
      <CookieConsent />
    </div>
  )
}
