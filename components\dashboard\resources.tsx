"use client"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import {
  Calendar,
  BarChart3,
  Newspaper,
  TrendingUp,
  TrendingDown,
  Globe,
  Clock,
  ExternalLink,
  ChevronRight,
  Activity,
  DollarSign
} from "lucide-react"
import { useState } from "react"
import Link from "next/link"

export default function Resources() {
  const [activeSection, setActiveSection] = useState("overview")

  const resourceCategories = [
    {
      id: "economic-calendar",
      title: "Economic Calendar",
      description: "Stay updated with important economic events and announcements",
      icon: Calendar,
      color: "blue",
      items: 8,
      lastUpdate: "2 minutes ago"
    },
    {
      id: "trading-symbols",
      title: "Trading Symbols",
      description: "Real-time prices and analysis for all trading instruments",
      icon: BarChart3,
      color: "green",
      items: 45,
      lastUpdate: "Live"
    },
    {
      id: "market-news",
      title: "Market News",
      description: "Latest financial news and market analysis",
      icon: Newspaper,
      color: "purple",
      items: 12,
      lastUpdate: "5 minutes ago"
    }
  ]

  const quickStats = [
    {
      title: "Active Markets",
      value: "24",
      change: "+2",
      icon: Globe,
      color: "blue"
    },
    {
      title: "Today's Events",
      value: "8",
      change: "+3",
      icon: Calendar,
      color: "orange"
    },
    {
      title: "Market Volatility",
      value: "Medium",
      change: "Stable",
      icon: Activity,
      color: "green"
    }
  ]

  const upcomingEvents = [
    {
      time: "14:30",
      currency: "USD",
      flag: "🇺🇸",
      event: "Federal Reserve Interest Rate Decision",
      impact: "high"
    },
    {
      time: "16:00",
      currency: "EUR",
      flag: "🇪🇺",
      event: "ECB Press Conference",
      impact: "high"
    },
    {
      time: "18:30",
      currency: "GBP",
      flag: "🇬🇧",
      event: "UK Inflation Rate",
      impact: "medium"
    }
  ]

  const topSymbols = [
    {
      symbol: "EURUSD",
      price: "1.0856",
      change: "+0.21%",
      trend: "up"
    },
    {
      symbol: "GBPUSD",
      price: "1.2734",
      change: "-0.35%",
      trend: "down"
    },
    {
      symbol: "USDJPY",
      price: "149.85",
      change: "+0.45%",
      trend: "up"
    }
  ]

  const latestNews = [
    {
      title: "Federal Reserve Signals Potential Rate Cut",
      time: "2 hours ago",
      category: "Central Banks"
    },
    {
      title: "EUR/USD Breaks Key Resistance Level",
      time: "4 hours ago",
      category: "Technical Analysis"
    },
    {
      title: "Oil Prices Surge on Supply Concerns",
      time: "6 hours ago",
      category: "Commodities"
    }
  ]

  const getColorClasses = (color: string) => {
    const colors = {
      blue: "from-blue-500 to-cyan-500 border-blue-200 dark:border-blue-400/20",
      green: "from-green-500 to-emerald-500 border-green-200 dark:border-green-400/20",
      purple: "from-purple-500 to-pink-500 border-purple-200 dark:border-purple-400/20",
      orange: "from-orange-500 to-red-500 border-orange-200 dark:border-orange-400/20"
    }
    return colors[color as keyof typeof colors] || colors.blue
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 dark:from-purple-500/20 dark:to-pink-500/20 backdrop-blur-sm border border-purple-200 dark:border-purple-400/20 rounded-3xl p-8 shadow-xl">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Trading Resources 📚</h1>
            <p className="text-lg text-gray-700 dark:text-white/70">Access all your trading tools and market information</p>
          </div>
          <div className="flex items-center gap-3">
            <Badge className="bg-purple-600 dark:bg-purple-500 text-white">
              <Activity className="w-4 h-4 mr-1" />
              Live Data
            </Badge>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {quickStats.map((stat, index) => {
            const Icon = stat.icon
            const colors = getColorClasses(stat.color)
            return (
              <div key={index} className={`bg-gradient-to-br ${colors} backdrop-blur-sm border rounded-2xl p-4`}>
                <div className="flex items-center justify-between">
                  <div className={`w-10 h-10 rounded-xl bg-gradient-to-r ${colors} flex items-center justify-center shadow-lg`}>
                    <Icon className="w-5 h-5 text-white" />
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">{stat.value}</div>
                    <div className="text-sm text-gray-600 dark:text-white/60">{stat.change}</div>
                  </div>
                </div>
                <div className="mt-2 text-sm font-medium text-gray-700 dark:text-white/80">{stat.title}</div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Resource Categories */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {resourceCategories.map((category) => {
          const Icon = category.icon
          const colors = getColorClasses(category.color)
          return (
            <div key={category.id} className={`bg-gradient-to-br ${colors} backdrop-blur-sm border rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 cursor-pointer group`}>
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 rounded-2xl bg-gradient-to-r ${colors} flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
                <Badge className="bg-white/20 text-gray-700 dark:text-white">
                  {category.items} items
                </Badge>
              </div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{category.title}</h3>
              <p className="text-gray-600 dark:text-white/70 mb-4">{category.description}</p>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500 dark:text-white/50">Updated {category.lastUpdate}</span>
                <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-white transition-colors duration-300" />
              </div>
            </div>
          )
        })}
      </div>

      {/* Content Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Upcoming Economic Events */}
        <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-6 shadow-xl">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              Upcoming Events
            </h3>
            <Link href="/economic-calendar">
              <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-700">
                View All <ExternalLink className="w-4 h-4 ml-1" />
              </Button>
            </Link>
          </div>
          <div className="space-y-4">
            {upcomingEvents.map((event, index) => (
              <div key={index} className="flex items-center gap-4 p-3 rounded-xl bg-gray-50 dark:bg-white/5 hover:bg-gray-100 dark:hover:bg-white/10 transition-colors duration-200">
                <div className="text-center">
                  <div className="text-lg font-bold text-gray-900 dark:text-white">{event.time}</div>
                  <div className="text-xs text-gray-500 dark:text-white/50">GMT</div>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-2xl">{event.flag}</span>
                  <Badge variant="secondary" className="text-xs">{event.currency}</Badge>
                </div>
                <div className="flex-1">
                  <div className="font-medium text-gray-900 dark:text-white">{event.event}</div>
                  <Badge className={`text-xs ${event.impact === 'high' ? 'bg-red-500' : 'bg-orange-500'} text-white`}>
                    {event.impact} impact
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Top Trading Symbols */}
        <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-6 shadow-xl">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              Top Symbols
            </h3>
            <Link href="/trading-symbols">
              <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-700">
                View All <ExternalLink className="w-4 h-4 ml-1" />
              </Button>
            </Link>
          </div>
          <div className="space-y-4">
            {topSymbols.map((symbol, index) => (
              <div key={index} className="flex items-center justify-between p-3 rounded-xl bg-gray-50 dark:bg-white/5 hover:bg-gray-100 dark:hover:bg-white/10 transition-colors duration-200">
                <div>
                  <div className="font-bold text-gray-900 dark:text-white">{symbol.symbol}</div>
                  <div className="text-sm text-gray-500 dark:text-white/50">Major Pair</div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-gray-900 dark:text-white">{symbol.price}</div>
                  <div className={`text-sm flex items-center gap-1 ${symbol.trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                    {symbol.trend === 'up' ? <TrendingUp className="w-3 h-3" /> : <TrendingDown className="w-3 h-3" />}
                    {symbol.change}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Latest Market News */}
      <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-6 shadow-xl">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
            <Newspaper className="w-5 h-5" />
            Latest Market News
          </h3>
          <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-700">
            View All <ExternalLink className="w-4 h-4 ml-1" />
          </Button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {latestNews.map((news, index) => (
            <div key={index} className="p-4 rounded-xl bg-gray-50 dark:bg-white/5 hover:bg-gray-100 dark:hover:bg-white/10 transition-colors duration-200 cursor-pointer">
              <Badge variant="secondary" className="text-xs mb-2">{news.category}</Badge>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2 line-clamp-2">{news.title}</h4>
              <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-white/50">
                <Clock className="w-3 h-3" />
                {news.time}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
