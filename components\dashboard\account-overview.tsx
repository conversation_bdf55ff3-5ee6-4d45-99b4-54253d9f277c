"use client"

import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import {
  TrendingUp,
  DollarSign,
  Target,
  BarChart3,
  Award,
  Shield,
  Zap,
  ArrowUpRight,
  ArrowDownRight,
  Eye,
  Download,
  BookOpen,
} from "lucide-react"
import { useState } from "react"

export default function AccountOverview() {
  const [selectedPeriod, setSelectedPeriod] = useState("7d")

  const accountStats = [
    {
      title: "Account Balance",
      value: "$25,000.00",
      change: "+$2,450.00",
      changePercent: "+10.85%",
      trend: "up",
      icon: DollarSign,
      color: "blue",
    },
    {
      title: "Total Profit",
      value: "$3,250.00",
      change: "+$450.00",
      changePercent: "+16.07%",
      trend: "up",
      icon: TrendingUp,
      color: "blue",
    },
    {
      title: "Win Rate",
      value: "78.5%",
      change: "+2.3%",
      changePercent: "+3.02%",
      trend: "up",
      icon: Target,
      color: "purple",
    },
    {
      title: "Daily Drawdown",
      value: "2.1%",
      change: "-0.5%",
      changePercent: "-19.23%",
      trend: "down",
      icon: Shield,
      color: "orange",
    },
  ]

  const recentTrades = [
    {
      pair: "EURUSD",
      type: "BUY",
      lots: "0.5",
      openPrice: "1.0856",
      closePrice: "1.0889",
      profit: "+$165.00",
      time: "2 hours ago",
      status: "closed",
    },
    {
      pair: "GBPUSD",
      type: "SELL",
      lots: "0.3",
      openPrice: "1.2734",
      closePrice: "1.2698",
      profit: "+$108.00",
      time: "4 hours ago",
      status: "closed",
    },
    {
      pair: "USDJPY",
      type: "BUY",
      lots: "0.2",
      openPrice: "149.85",
      closePrice: "150.12",
      profit: "+$54.00",
      time: "6 hours ago",
      status: "closed",
    },
    {
      pair: "USDCAD",
      type: "SELL",
      lots: "0.4",
      openPrice: "1.3567",
      closePrice: "-",
      profit: "-$23.00",
      time: "1 hour ago",
      status: "open",
    },
  ]

  const challengeProgress = {
    currentPhase: "Phase 1",
    profitTarget: "$2,000.00",
    currentProfit: "$1,250.00",
    progress: 62.5,
    daysTraded: 8,
    minDays: 4,
    maxDrawdown: "10%",
    currentDrawdown: "2.1%",
    timeRemaining: "Unlimited",
  }

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: {
        bg: "from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20",
        border: "border-blue-200 dark:border-blue-400/20",
        icon: "text-blue-600 dark:text-blue-400",
        iconBg: "from-blue-500 to-cyan-500",
      },
      purple: {
        bg: "from-purple-500/10 to-pink-500/10 dark:from-purple-500/20 dark:to-pink-500/20",
        border: "border-purple-200 dark:border-purple-400/20",
        icon: "text-purple-600 dark:text-purple-400",
        iconBg: "from-purple-500 to-pink-500",
      },
      orange: {
        bg: "from-orange-500/10 to-amber-500/10 dark:from-orange-500/20 dark:to-amber-500/20",
        border: "border-orange-200 dark:border-orange-400/20",
        icon: "text-orange-600 dark:text-orange-400",
        iconBg: "from-orange-500 to-amber-500",
      },
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.blue
  }

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-8 shadow-xl relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10" />
        <div className="relative z-10">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Welcome back, John! 👋</h1>
              <p className="text-lg text-gray-700 dark:text-white/70">Here's your trading performance overview</p>
            </div>
            <div className="flex items-center gap-3">
              <Badge className="bg-blue-600 dark:bg-blue-500 text-white">
                <Award className="w-4 h-4 mr-1" />
                Phase 1 Active
              </Badge>
              <Button className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-500 dark:to-cyan-500 text-white hover:from-blue-700 hover:to-cyan-700 dark:hover:from-blue-600 dark:hover:to-cyan-600">
                <Eye className="w-4 h-4 mr-2" />
                View Challenge
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Apex Capital Info & Account Types */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* About Apex Capital */}
        <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-6 shadow-xl">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center shadow-lg">
              <Shield className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white">About Apex Capital</h3>
              <p className="text-sm text-gray-600 dark:text-white/60">Your trusted trading partner</p>
            </div>
          </div>
          <div className="space-y-3 text-gray-700 dark:text-white/80">
            <p>Apex Capital is a leading proprietary trading firm that provides funded trading accounts to skilled traders worldwide.</p>
            <p>We offer competitive profit splits, flexible trading rules, and comprehensive support to help you succeed in the financial markets.</p>
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div className="text-center p-3 rounded-xl bg-blue-50 dark:bg-blue-500/10">
                <div className="text-2xl font-bold text-blue-600">$10M+</div>
                <div className="text-xs text-gray-600 dark:text-white/60">Funded Capital</div>
              </div>
              <div className="text-center p-3 rounded-xl bg-green-50 dark:bg-green-500/10">
                <div className="text-2xl font-bold text-green-600">5000+</div>
                <div className="text-xs text-gray-600 dark:text-white/60">Active Traders</div>
              </div>
            </div>
          </div>
        </div>

        {/* Account Types */}
        <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-6 shadow-xl">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-purple-600 to-pink-600 flex items-center justify-center shadow-lg">
                <Award className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">Account Types</h3>
                <p className="text-sm text-gray-600 dark:text-white/60">Choose your trading challenge</p>
              </div>
            </div>
            <Button className="bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:from-purple-700 hover:to-pink-700">
              Buy Account
            </Button>
          </div>

          <div className="space-y-3">
            <div className="p-4 rounded-xl bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-500/10 dark:to-cyan-500/10 border border-blue-200 dark:border-blue-400/20">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-bold text-gray-900 dark:text-white">HFT Phase 1</h4>
                <Badge className="bg-blue-600 text-white">Popular</Badge>
              </div>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span className="text-gray-600 dark:text-white/60">Account Size:</span>
                  <span className="font-semibold text-gray-900 dark:text-white ml-1">$25K - $200K</span>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-white/60">Profit Target:</span>
                  <span className="font-semibold text-gray-900 dark:text-white ml-1">8%</span>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-white/60">Max Drawdown:</span>
                  <span className="font-semibold text-gray-900 dark:text-white ml-1">5%</span>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-white/60">Time Limit:</span>
                  <span className="font-semibold text-gray-900 dark:text-white ml-1">30 days</span>
                </div>
              </div>
            </div>

            <div className="p-4 rounded-xl bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-500/10 dark:to-emerald-500/10 border border-green-200 dark:border-green-400/20">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-bold text-gray-900 dark:text-white">HFT Phase 2</h4>
                <Badge className="bg-green-600 text-white">Advanced</Badge>
              </div>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span className="text-gray-600 dark:text-white/60">Account Size:</span>
                  <span className="font-semibold text-gray-900 dark:text-white ml-1">$25K - $200K</span>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-white/60">Profit Target:</span>
                  <span className="font-semibold text-gray-900 dark:text-white ml-1">5%</span>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-white/60">Max Drawdown:</span>
                  <span className="font-semibold text-gray-900 dark:text-white ml-1">5%</span>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-white/60">Time Limit:</span>
                  <span className="font-semibold text-gray-900 dark:text-white ml-1">60 days</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Account Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {accountStats.map((stat, index) => {
          const colors = getColorClasses(stat.color)
          const IconComponent = stat.icon
          const isPositive = stat.trend === "up"

          return (
            <div
              key={index}
              className={`bg-gradient-to-br ${colors.bg} backdrop-blur-sm border ${colors.border} rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-[1.02] group`}
            >
              <div className="flex items-center justify-between mb-4">
                <div
                  className={`w-12 h-12 rounded-2xl bg-gradient-to-r ${colors.iconBg} flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}
                >
                  <IconComponent className="w-6 h-6 text-white" />
                </div>
                <div
                  className={`flex items-center gap-1 text-sm font-medium ${
                    isPositive ? "text-blue-600 dark:text-blue-400" : "text-red-600 dark:text-red-400"
                  }`}
                >
                  {isPositive ? <ArrowUpRight className="w-4 h-4" /> : <ArrowDownRight className="w-4 h-4" />}
                  {stat.changePercent}
                </div>
              </div>
              <div className="mb-2">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">{stat.value}</div>
                <div className="text-sm text-gray-600 dark:text-white/60">{stat.title}</div>
              </div>
              <div
                className={`text-sm font-medium ${
                  isPositive ? "text-blue-600 dark:text-blue-400" : "text-red-600 dark:text-red-400"
                }`}
              >
                {stat.change} today
              </div>
            </div>
          )
        })}
      </div>

      {/* Challenge Progress */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-xl">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white">Challenge Progress</h3>
              <Badge className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400">
                {challengeProgress.currentPhase}
              </Badge>
            </div>

            <div className="space-y-6">
              {/* Profit Target Progress */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <span className="text-sm font-medium text-gray-700 dark:text-white/70">Profit Target</span>
                  <span className="text-sm font-bold text-gray-900 dark:text-white">
                    {challengeProgress.currentProfit} / {challengeProgress.profitTarget}
                  </span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                  <div
                    className="bg-gradient-to-r from-blue-500 to-cyan-500 h-3 rounded-full transition-all duration-500"
                    style={{ width: `${challengeProgress.progress}%` }}
                  />
                </div>
                <div className="text-right text-sm text-gray-600 dark:text-white/60 mt-1">
                  {challengeProgress.progress}% Complete
                </div>
              </div>

              {/* Challenge Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-gray-50 dark:bg-white/5 rounded-2xl">
                  <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                    {challengeProgress.daysTraded}
                  </div>
                  <div className="text-xs text-gray-600 dark:text-white/60">Days Traded</div>
                  <div className="text-xs text-blue-600 dark:text-blue-400 mt-1">Min: {challengeProgress.minDays}</div>
                </div>
                <div className="text-center p-4 bg-gray-50 dark:bg-white/5 rounded-2xl">
                  <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                    {challengeProgress.currentDrawdown}
                  </div>
                  <div className="text-xs text-gray-600 dark:text-white/60">Current DD</div>
                  <div className="text-xs text-orange-600 dark:text-orange-400 mt-1">
                    Max: {challengeProgress.maxDrawdown}
                  </div>
                </div>
                <div className="text-center p-4 bg-gray-50 dark:bg-white/5 rounded-2xl">
                  <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">78.5%</div>
                  <div className="text-xs text-gray-600 dark:text-white/60">Win Rate</div>
                  <div className="text-xs text-blue-600 dark:text-blue-400 mt-1">Excellent</div>
                </div>
                <div className="text-center p-4 bg-gray-50 dark:bg-white/5 rounded-2xl">
                  <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">∞</div>
                  <div className="text-xs text-gray-600 dark:text-white/60">Time Left</div>
                  <div className="text-xs text-blue-600 dark:text-blue-400 mt-1">Unlimited</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="space-y-6">
          <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-6 shadow-xl">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">Quick Actions</h3>
            <div className="space-y-4">
              <Button className="w-full bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-500 dark:to-cyan-500 text-white hover:from-blue-700 hover:to-cyan-700 dark:hover:from-blue-600 dark:hover:to-cyan-600 py-3">
                <TrendingUp className="w-4 h-4 mr-2" />
                Open Trade
              </Button>
              <Button
                variant="outline"
                className="w-full border-gray-300 dark:border-white/20 text-gray-700 dark:text-white/70 hover:bg-gray-50 dark:hover:bg-white/5 py-3 bg-transparent"
              >
                <Download className="w-4 h-4 mr-2" />
                Download Report
              </Button>
              <Button
                variant="outline"
                className="w-full border-gray-300 dark:border-white/20 text-gray-700 dark:text-white/70 hover:bg-gray-50 dark:hover:bg-white/5 py-3 bg-transparent"
              >
                <BarChart3 className="w-4 h-4 mr-2" />
                View Analytics
              </Button>
            </div>
          </div>

          <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-6 shadow-xl">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center">
                <Zap className="w-5 h-5 text-white" />
              </div>
              <div>
                <div className="font-bold text-gray-900 dark:text-white">Next Payout</div>
                <div className="text-sm text-gray-600 dark:text-white/60">In 3 days</div>
              </div>
            </div>
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-2">$2,925.00</div>
            <div className="text-sm text-gray-600 dark:text-white/60">
              Available for withdrawal after minimum trading days
            </div>
          </div>
        </div>
      </div>

      {/* Recent Trades */}
      <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-xl">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white">Recent Trades</h3>
          <div className="flex items-center gap-2">
            {["1d", "7d", "30d", "90d"].map((period) => (
              <Button
                key={period}
                variant={selectedPeriod === period ? "default" : "ghost"}
                size="sm"
                onClick={() => setSelectedPeriod(period)}
                className={
                  selectedPeriod === period
                    ? "bg-blue-600 hover:bg-blue-700 text-white"
                    : "text-gray-600 dark:text-white/60"
                }
              >
                {period}
              </Button>
            ))}
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200 dark:border-white/10">
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">Pair</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">Type</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">Lots</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">Open</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">Close</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">P&L</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">Time</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">Status</th>
              </tr>
            </thead>
            <tbody>
              {recentTrades.map((trade, index) => (
                <tr
                  key={index}
                  className="border-b border-gray-100 dark:border-white/5 hover:bg-gray-50/50 dark:hover:bg-white/5 transition-colors duration-200"
                >
                  <td className="py-4 px-4">
                    <div className="font-bold text-gray-900 dark:text-white">{trade.pair}</div>
                  </td>
                  <td className="py-4 px-4">
                    <Badge
                      className={
                        trade.type === "BUY"
                          ? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400"
                          : "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400"
                      }
                    >
                      {trade.type}
                    </Badge>
                  </td>
                  <td className="py-4 px-4 text-gray-700 dark:text-white/70">{trade.lots}</td>
                  <td className="py-4 px-4 text-gray-700 dark:text-white/70">{trade.openPrice}</td>
                  <td className="py-4 px-4 text-gray-700 dark:text-white/70">{trade.closePrice}</td>
                  <td className="py-4 px-4">
                    <span
                      className={`font-bold ${
                        trade.profit.startsWith("+")
                          ? "text-blue-600 dark:text-blue-400"
                          : "text-red-600 dark:text-red-400"
                      }`}
                    >
                      {trade.profit}
                    </span>
                  </td>
                  <td className="py-4 px-4 text-gray-600 dark:text-white/60">{trade.time}</td>
                  <td className="py-4 px-4">
                    <Badge
                      className={
                        trade.status === "closed"
                          ? "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
                          : "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400"
                      }
                    >
                      {trade.status}
                    </Badge>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Trading Rules & Guidelines */}
      <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-6 shadow-xl">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-orange-600 to-red-600 flex items-center justify-center shadow-lg">
            <BookOpen className="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-white">Trading Rules & Guidelines</h3>
            <p className="text-sm text-gray-600 dark:text-white/60">Important rules for all account types</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* General Rules */}
          <div className="space-y-3">
            <h4 className="font-bold text-gray-900 dark:text-white flex items-center gap-2">
              <Shield className="w-4 h-4 text-blue-500" />
              General Rules
            </h4>
            <ul className="space-y-2 text-sm text-gray-700 dark:text-white/80">
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 rounded-full bg-blue-500 mt-2 flex-shrink-0" />
                No news trading 2 minutes before and after high-impact news
              </li>
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 rounded-full bg-blue-500 mt-2 flex-shrink-0" />
                Maximum 5% risk per trade
              </li>
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 rounded-full bg-blue-500 mt-2 flex-shrink-0" />
                No holding positions over weekends
              </li>
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 rounded-full bg-blue-500 mt-2 flex-shrink-0" />
                Minimum 5 trading days required
              </li>
            </ul>
          </div>

          {/* Risk Management */}
          <div className="space-y-3">
            <h4 className="font-bold text-gray-900 dark:text-white flex items-center gap-2">
              <Target className="w-4 h-4 text-orange-500" />
              Risk Management
            </h4>
            <ul className="space-y-2 text-sm text-gray-700 dark:text-white/80">
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 rounded-full bg-orange-500 mt-2 flex-shrink-0" />
                Daily drawdown limit: 5% of initial balance
              </li>
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 rounded-full bg-orange-500 mt-2 flex-shrink-0" />
                Maximum drawdown: 10% of initial balance
              </li>
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 rounded-full bg-orange-500 mt-2 flex-shrink-0" />
                Stop loss required on all positions
              </li>
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 rounded-full bg-orange-500 mt-2 flex-shrink-0" />
                No martingale or grid trading strategies
              </li>
            </ul>
          </div>

          {/* Profit Sharing */}
          <div className="space-y-3">
            <h4 className="font-bold text-gray-900 dark:text-white flex items-center gap-2">
              <DollarSign className="w-4 h-4 text-green-500" />
              Profit Sharing
            </h4>
            <ul className="space-y-2 text-sm text-gray-700 dark:text-white/80">
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 rounded-full bg-green-500 mt-2 flex-shrink-0" />
                Phase 1: Keep 80% of profits
              </li>
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 rounded-full bg-green-500 mt-2 flex-shrink-0" />
                Phase 2: Keep 80% of profits
              </li>
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 rounded-full bg-green-500 mt-2 flex-shrink-0" />
                Funded Account: Keep 80% of profits
              </li>
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 rounded-full bg-green-500 mt-2 flex-shrink-0" />
                Bi-weekly payout schedule
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-6 p-4 rounded-xl bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-500/10 dark:to-cyan-500/10 border border-blue-200 dark:border-blue-400/20">
          <div className="flex items-center gap-2 mb-2">
            <Award className="w-5 h-5 text-blue-600" />
            <span className="font-semibold text-gray-900 dark:text-white">Pro Tip</span>
          </div>
          <p className="text-sm text-gray-700 dark:text-white/80">
            Focus on consistent profitability rather than large gains. Our most successful traders maintain steady growth with proper risk management.
          </p>
        </div>
      </div>
    </div>
  )
}
